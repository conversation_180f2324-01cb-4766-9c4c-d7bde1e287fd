'use client';

import React from 'react';
import {
  Target,
  TrendingUp,
  BookOpen,
  Star,
  Calendar,
  Award,
  Lightbulb,
  Users,
  CheckCircle,
  ArrowRight
} from 'lucide-react';

interface TestResult {
  overallBandScore: number | null;
  listeningBandScore: number | null;
  readingBandScore: number | null;
  writingBandScore: number | null;
  speakingBandScore: number | null;
}

interface SingleTestMotivationProps {
  result: TestResult;
  candidateName: string;
  className?: string;
}

export default function SingleTestMotivation({
  result,
  candidateName,
  className = ''
}: SingleTestMotivationProps) {
  const overallScore = result.overallBandScore || 0;

  // Determine performance level and messaging
  const getPerformanceLevel = (score: number) => {
    if (score >= 8.5) return {
      level: 'Excellent',
      colorClasses: {
        bg: 'bg-gradient-to-r from-emerald-50 to-emerald-100',
        text: 'text-emerald-700',
        icon: 'text-emerald-600',
        accent: 'text-emerald-600'
      },
      message: 'Outstanding performance!'
    };
    if (score >= 7.5) return {
      level: 'Very Good',
      colorClasses: {
        bg: 'bg-gradient-to-r from-blue-50 to-blue-100',
        text: 'text-blue-700',
        icon: 'text-blue-600',
        accent: 'text-blue-600'
      },
      message: 'Strong English proficiency!'
    };
    if (score >= 6.5) return {
      level: 'Good',
      colorClasses: {
        bg: 'bg-gradient-to-r from-indigo-50 to-indigo-100',
        text: 'text-indigo-700',
        icon: 'text-indigo-600',
        accent: 'text-indigo-600'
      },
      message: 'Solid foundation to build on!'
    };
    if (score >= 5.5) return {
      level: 'Moderate',
      colorClasses: {
        bg: 'bg-gradient-to-r from-yellow-50 to-yellow-100',
        text: 'text-yellow-700',
        icon: 'text-yellow-600',
        accent: 'text-yellow-600'
      },
      message: 'Great potential for improvement!'
    };
    return {
      level: 'Limited',
      colorClasses: {
        bg: 'bg-gradient-to-r from-orange-50 to-orange-100',
        text: 'text-orange-700',
        icon: 'text-orange-600',
        accent: 'text-orange-600'
      },
      message: 'Every expert was once a beginner!'
    };
  };

  const performance = getPerformanceLevel(overallScore);

  // Calculate improvement potential
  const getImprovementPotential = () => {
    const scores = [
      result.listeningBandScore,
      result.readingBandScore,
      result.writingBandScore,
      result.speakingBandScore
    ].filter(score => score !== null) as number[];

    const lowestScore = Math.min(...scores);
    const highestScore = Math.max(...scores);
    const gap = highestScore - lowestScore;

    if (gap >= 1.5) {
      return {
        potential: 'High',
        message: 'Significant improvement possible by focusing on weaker areas',
        targetIncrease: 1.0
      };
    } else if (gap >= 1.0) {
      return {
        potential: 'Moderate',
        message: 'Good improvement possible with consistent practice',
        targetIncrease: 0.5
      };
    } else {
      return {
        potential: 'Steady',
        message: 'Consistent improvement across all skills',
        targetIncrease: 0.5
      };
    }
  };

  const improvement = getImprovementPotential();
  const targetScore = Math.min(9, overallScore + improvement.targetIncrease);

  // Study recommendations based on current level
  const getStudyRecommendations = () => {
    if (overallScore >= 7.5) {
      return [
        'Focus on advanced vocabulary and complex grammar structures',
        'Practice academic writing and formal speaking',
        'Work on consistency across all four skills',
        'Take practice tests under timed conditions'
      ];
    } else if (overallScore >= 6.5) {
      return [
        'Expand vocabulary range and accuracy',
        'Improve grammatical accuracy and complexity',
        'Practice specific IELTS task types',
        'Focus on time management strategies'
      ];
    } else {
      return [
        'Build fundamental vocabulary and grammar',
        'Practice basic communication skills daily',
        'Focus on understanding IELTS test format',
        'Start with easier practice materials'
      ];
    }
  };

  const studyRecommendations = getStudyRecommendations();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Welcome Message */}
      <div className={`card-elevated ${performance.colorClasses.bg} animate-slide-up`}>
        <div className="p-6">
          <div className="flex items-center mb-4">
            <Star className={`h-8 w-8 ${performance.colorClasses.icon} mr-3`} />
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                Welcome to Your IELTS Journey, {candidateName}!
              </h2>
              <p className={`${performance.colorClasses.text} font-medium`}>
                {performance.level} Performance - {performance.message}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">{overallScore}</div>
              <div className="text-sm text-gray-600">Current Band Score</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600">{targetScore}</div>
              <div className="text-sm text-gray-600">Target Score</div>
            </div>
            <div className="text-center">
              <div className={`text-3xl font-bold ${performance.colorClasses.accent}`}>
                +{improvement.targetIncrease}
              </div>
              <div className="text-sm text-gray-600">Potential Increase</div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Potential */}
      <div className="card-elevated animate-slide-up" style={{ animationDelay: '200ms' }}>
        <div className="p-6">
          <div className="flex items-center mb-4">
            <TrendingUp className="h-6 w-6 text-green-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Your Improvement Potential</h3>
          </div>

          <div className="bg-green-50 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-green-800">Improvement Potential: {improvement.potential}</span>
              <span className="text-sm text-green-600">{improvement.message}</span>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-green-200 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-green-500 to-green-600 h-3 rounded-full transition-all duration-1000 ease-out"
                style={{ width: `${(improvement.targetIncrease / 2) * 100}%` }}
              />
            </div>
          </div>

          <p className="text-gray-700 text-sm">
            Based on your current performance, you have excellent potential for improvement.
            With focused practice and the right study plan, you can achieve your target band score!
          </p>
        </div>
      </div>

      {/* Next Steps */}
      <div className="card-elevated animate-slide-up" style={{ animationDelay: '400ms' }}>
        <div className="p-6">
          <div className="flex items-center mb-4">
            <Target className="h-6 w-6 text-blue-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Your Next Steps</h3>
          </div>

          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                1
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Take More Practice Tests</h4>
                <p className="text-sm text-gray-600">
                  Regular practice tests will help track your progress and identify areas for improvement.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                2
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Focus on Weak Areas</h4>
                <p className="text-sm text-gray-600">
                  Identify your lowest-scoring modules and dedicate extra practice time to them.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                3
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Create a Study Schedule</h4>
                <p className="text-sm text-gray-600">
                  Consistent daily practice is key to improvement. Aim for 1-2 hours of focused study.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Study Recommendations */}
      <div className="card-elevated animate-slide-up" style={{ animationDelay: '600ms' }}>
        <div className="p-6">
          <div className="flex items-center mb-4">
            <BookOpen className="h-6 w-6 text-purple-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Personalized Study Plan</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Recommended Focus Areas</h4>
              <div className="space-y-2">
                {studyRecommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{recommendation}</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-3">Study Schedule</h4>
              <div className="space-y-2 text-sm text-gray-700">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-blue-500" />
                  <span>Daily practice: {overallScore >= 7 ? '1-2 hours' : '2-3 hours'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-green-500" />
                  <span>Speaking practice: 3x per week</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Award className="h-4 w-4 text-purple-500" />
                  <span>Mock tests: Weekly</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Lightbulb className="h-4 w-4 text-yellow-500" />
                  <span>Vocabulary: 15 minutes daily</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Motivation Message */}
      <div className="card-elevated bg-gradient-to-r from-purple-50 to-indigo-50 animate-slide-up" style={{ animationDelay: '800ms' }}>
        <div className="p-6 text-center">
          <div className="flex items-center justify-center mb-4">
            <Award className="h-8 w-8 text-purple-600 mr-2" />
            <h3 className="text-lg font-semibold text-purple-900">You're Just Getting Started!</h3>
          </div>
          <p className="text-purple-700 mb-4">
            This is your first step on an exciting journey to English proficiency.
            Every expert was once a beginner, and with dedication and practice, you'll see amazing progress!
          </p>
          <div className="flex items-center justify-center text-sm text-purple-600">
            <span>Ready to improve your score?</span>
            <ArrowRight className="h-4 w-4 ml-1" />
          </div>
        </div>
      </div>
    </div>
  );
}
