import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

async function testAPIEndpoints() {
  const baseUrl = 'http://localhost:3001';

  try {
    console.log('🧪 Testing API endpoints after schema changes...\n');

    // Test 1: Admin candidates list
    console.log('1️⃣ Testing admin candidates list...');
    try {
      const response = await fetch(`${baseUrl}/api/admin/candidates?page=1&limit=5`);
      const data = await response.json();

      if (response.ok) {
        console.log(`✅ Admin candidates API working - found ${data.total} candidates`);
        if (data.candidates && data.candidates.length > 0) {
          console.log(`   Sample candidate: ${data.candidates[0].fullName} (${data.candidates[0].candidateNumber})`);
        }
      } else {
        console.log(`❌ Admin candidates API failed: ${data.error}`);
      }
    } catch (error) {
      console.log(`❌ Admin candidates API error: ${error.message}`);
    }

    // Test 2: Checker candidates list (Quick Entry)
    console.log('\n2️⃣ Testing checker candidates list (Quick Entry)...');
    try {
      const testDate = '2025-05-31'; // Use a date that has registrations
      const response = await fetch(`${baseUrl}/api/checker/candidates?includeResults=true&testDate=${testDate}`);
      const data = await response.json();

      if (response.ok) {
        console.log(`✅ Checker candidates API working - found ${data.total} candidates for ${testDate}`);
        if (data.candidates && data.candidates.length > 0) {
          console.log(`   Sample candidate: ${data.candidates[0].fullName} (${data.candidates[0].candidateNumber})`);
          console.log(`   Has result: ${data.candidates[0].hasResult}`);
        }
      } else {
        console.log(`❌ Checker candidates API failed: ${data.error}`);
      }
    } catch (error) {
      console.log(`❌ Checker candidates API error: ${error.message}`);
    }

    // Test 3: Admin candidate search
    console.log('\n3️⃣ Testing admin candidate search...');
    try {
      const response = await fetch(`${baseUrl}/api/admin/candidates/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: 'AD329423' // Use a known passport number
        })
      });
      const data = await response.json();

      if (response.ok) {
        console.log(`✅ Admin search API working - found ${data.length} candidates`);
        if (data.length > 0) {
          console.log(`   Found candidate: ${data[0].fullName}`);
          console.log(`   Test registrations: ${data[0].testRegistrations?.length || 0}`);
        }
      } else {
        console.log(`❌ Admin search API failed: ${data.error}`);
      }
    } catch (error) {
      console.log(`❌ Admin search API error: ${error.message}`);
    }

    // Test 4: Checker candidate search
    console.log('\n4️⃣ Testing checker candidate search...');
    try {
      const response = await fetch(`${baseUrl}/api/checker/candidates/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: 'AD329423', // Use a known passport number
          searchType: 'passport'
        })
      });
      const data = await response.json();

      if (response.ok) {
        console.log(`✅ Checker search API working - found ${data.length} candidates`);
        if (data.length > 0) {
          console.log(`   Found candidate: ${data[0].fullName}`);
          console.log(`   Test registrations: ${data[0].testRegistrations?.length || 0}`);
        }
      } else {
        console.log(`❌ Checker search API failed: ${data.error}`);
      }
    } catch (error) {
      console.log(`❌ Checker search API error: ${error.message}`);
    }

    // Test 5: Test duplicate registration prevention
    console.log('\n5️⃣ Testing duplicate registration prevention...');
    try {
      const duplicateData = {
        fullName: 'Test Duplicate',
        email: '<EMAIL>',
        phoneNumber: '+1234567890',
        dateOfBirth: '1990-01-01',
        nationality: 'Test Country',
        passportNumber: 'TEST123456',
        testDate: '2025-06-01',
        testCenter: 'Innovative Centre - Samarkand'
      };

      // First registration
      const response1 = await fetch(`${baseUrl}/api/admin/candidates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(duplicateData)
      });
      const data1 = await response1.json();

      if (response1.ok) {
        console.log(`✅ First registration successful: ${data1.fullName} (${data1.candidateNumber})`);

        // Attempt duplicate registration
        const response2 = await fetch(`${baseUrl}/api/admin/candidates`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(duplicateData)
        });
        const data2 = await response2.json();

        if (response2.status === 409) {
          console.log(`✅ Duplicate prevention working: ${data2.error}`);
        } else {
          console.log(`❌ Duplicate prevention failed - should have returned 409 status`);
        }

        // Clean up test data
        console.log('🧹 Cleaning up test data...');
        // Note: In a real scenario, you'd want to clean up the test registration
      } else {
        console.log(`❌ First registration failed: ${data1.error}`);
      }
    } catch (error) {
      console.log(`❌ Duplicate registration test error: ${error.message}`);
    }

    console.log('\n🎉 API endpoint testing completed!');

  } catch (error) {
    console.error('❌ Test script failed:', error);
    throw error;
  }
}

// Run the tests
testAPIEndpoints().catch((error) => {
  console.error('Test script failed:', error);
  process.exit(1);
});
