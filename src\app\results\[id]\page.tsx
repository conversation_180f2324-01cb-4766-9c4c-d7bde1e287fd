'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  ArrowLeft,
  Calendar,
  MapPin,
  User,
  FileText,
  Activity,
  CheckCircle,
  Clock,
  AlertCircle,
  Award
} from 'lucide-react';
import HorizontalNavigationMenu from '@/components/results/HorizontalNavigationMenu';
import PerformanceInsights from '@/components/charts/PerformanceInsights';
import EnhancedProgressChart from '@/components/charts/EnhancedProgressChart';

interface TestResult {
  id: string;
  testRegistrationId: string;
  testDate: string;
  listeningScore: number | null;
  listeningBandScore: number | null;
  readingScore: number | null;
  readingBandScore: number | null;
  writingTask1Score: number | null;
  writingTask2Score: number | null;
  writingBandScore: number | null;
  speakingFluencyScore: number | null;
  speakingLexicalScore: number | null;
  speakingGrammarScore: number | null;
  speakingPronunciationScore: number | null;
  speakingBandScore: number | null;
  overallBandScore: number | null;
  status: 'pending' | 'completed' | 'verified';
  certificateSerial: string | null;
  certificateGenerated: boolean;
  aiFeedbackGenerated: boolean;
  createdAt: string;
  updatedAt: string;
  candidate: {
    id: string;
    fullName: string;
    nationality: string;
    photoUrl: string | null;
  };
  testRegistration: {
    candidateNumber: string;
    testDate: string;
    testCenter: string;
  };
  performanceMetrics: {
    averageScore: number | null;
    highestScore: number | null;
    lowestScore: number | null;
    scoreDistribution: {
      listening?: number | null;
      reading?: number | null;
      writing?: number | null;
      speaking?: number | null;
    };
  };
}

export default function PublicResultsPage() {
  const params = useParams();
  const resultId = params.id as string;

  const [result, setResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchResult = useCallback(async () => {
    try {
      const response = await fetch(`/api/results/${resultId}`);
      if (response.ok) {
        const data = await response.json();
        setResult(data);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Result not found');
      }
    } catch (error) {
      console.error('Error fetching result:', error);
      setError('Failed to load result');
    } finally {
      setIsLoading(false);
    }
  }, [resultId]);

  useEffect(() => {
    fetchResult();
  }, [fetchResult]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'verified':
        return <Award className="h-5 w-5 text-blue-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-800 bg-yellow-100';
      case 'completed':
        return 'text-green-800 bg-green-100';
      case 'verified':
        return 'text-blue-800 bg-blue-100';
      default:
        return 'text-gray-800 bg-gray-100';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center animate-fade-in">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary-200 border-t-primary-600 mx-auto mb-6"></div>
            <div className="absolute inset-0 rounded-full h-16 w-16 border-4 border-transparent border-t-primary-300 animate-pulse mx-auto"></div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Your Results</h3>
          <p className="text-muted-foreground">Please wait while we fetch your IELTS test results...</p>
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8 animate-fade-in">
          <div className="p-4 bg-red-100 rounded-full w-fit mx-auto mb-6">
            <AlertCircle className="h-12 w-12 text-red-500" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-3">Unable to Load Results</h3>
          <p className="text-muted-foreground mb-8 leading-relaxed">{error || 'The requested result could not be found. Please check your result ID and try again.'}</p>
          <Link
            href="/search"
            className="btn-primary inline-flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Search
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-blue-50 to-indigo-100">
      <header className="bg-white/80 backdrop-blur-sm border-b border-white/20 shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link
                href="/search"
                className="flex items-center text-primary-600 hover:text-primary-700 mr-6 transition-colors duration-200 focus-ring rounded-md p-2 -m-2"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                <span className="font-medium">Back to Search</span>
              </Link>
              <div className="flex items-center">
                <div className="p-3 bg-primary-100 rounded-xl mr-4">
                  <FileText className="h-8 w-8 text-primary-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">IELTS Test Results</h1>
                  <p className="text-muted-foreground">Comprehensive results overview</p>
                </div>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm text-muted-foreground">Result ID</div>
                <div className="font-mono text-sm font-medium text-gray-900">{result.id.slice(0, 8)}...</div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Horizontal Navigation */}
      <HorizontalNavigationMenu
        resultId={resultId}
        completionStatus={{
          overview: true,
          progress: !!result.overallBandScore,
          feedback: result.aiFeedbackGenerated,
          certificate: result.certificateGenerated
        }}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-1 space-y-6">
            {/* Enhanced Candidate Information Card */}
            <div className="card-elevated animate-slide-up">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <User className="h-5 w-5 text-primary-600 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-900">Candidate Information</h2>
                </div>
                <div className="flex items-start space-x-4">
                  {result.candidate.photoUrl ? (
                    <div className="relative">
                      <Image
                        src={result.candidate.photoUrl}
                        alt={result.candidate.fullName}
                        width={80}
                        height={80}
                        className="rounded-xl object-cover shadow-md"
                      />
                      <div className="absolute -bottom-2 -right-2 p-1 bg-success-500 rounded-full">
                        <CheckCircle className="h-4 w-4 text-white" />
                      </div>
                    </div>
                  ) : (
                    <div className="w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl flex items-center justify-center shadow-md">
                      <User className="h-10 w-10 text-primary-600" />
                    </div>
                  )}
                  <div className="flex-1">
                    <h4 className="text-xl font-bold text-gray-900 mb-3">{result.candidate.fullName}</h4>
                    <div className="space-y-3">
                      <div className="flex items-center text-sm text-gray-600">
                        <div className="p-1 bg-blue-100 rounded mr-3">
                          <MapPin className="h-3 w-3 text-blue-600" />
                        </div>
                        <span className="font-medium">{result.candidate.nationality}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <div className="p-1 bg-emerald-100 rounded mr-3">
                          <Calendar className="h-3 w-3 text-emerald-600" />
                        </div>
                        <span>{new Date(result.testRegistration.testDate).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <div className="p-1 bg-purple-100 rounded mr-3">
                          <MapPin className="h-3 w-3 text-purple-600" />
                        </div>
                        <span>{result.testRegistration.testCenter}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Result Status Card */}
            <div className="card-elevated animate-slide-up" style={{ animationDelay: '150ms' }}>
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <Activity className="h-5 w-5 text-primary-600 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-900">Result Status</h2>
                </div>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    {getStatusIcon(result.status)}
                    <span className={`ml-3 px-4 py-2 rounded-full text-sm font-semibold ${getStatusBadge(result.status)}`}>
                      {result.status.charAt(0).toUpperCase() + result.status.slice(1)}
                    </span>
                  </div>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Result ID:</span>
                    <span className="font-mono font-medium text-gray-900">{result.id.slice(0, 12)}...</span>
                  </div>
                  {result.certificateSerial && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Certificate Serial:</span>
                      <span className="font-mono font-medium text-gray-900">{result.certificateSerial}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Generated:</span>
                    <span className="font-medium text-gray-900">
                      {new Date(result.createdAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Overall Band Score Card */}
            {result.overallBandScore && (
              <div className="relative overflow-hidden rounded-xl shadow-xl animate-slide-up" style={{ animationDelay: '300ms' }}>
                <div className="absolute inset-0 bg-gradient-to-br from-primary-500 via-primary-600 to-purple-600"></div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                <div className="relative p-8 text-white text-center">
                  <div className="mb-4">
                    <Award className="h-8 w-8 mx-auto mb-2 text-white/90" />
                    <h2 className="text-lg font-semibold text-white/90">Overall Band Score</h2>
                  </div>
                  <div className="mb-4">
                    <div className="text-6xl font-bold mb-2 text-white drop-shadow-lg">
                      {result.overallBandScore}
                    </div>
                    <div className="text-primary-100 font-medium">IELTS Band Score</div>
                  </div>
                  <div className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full">
                    <span className="text-sm font-semibold">
                      {result.overallBandScore >= 8.5 && 'Expert User'}
                      {result.overallBandScore >= 7.5 && result.overallBandScore < 8.5 && 'Very Good User'}
                      {result.overallBandScore >= 6.5 && result.overallBandScore < 7.5 && 'Good User'}
                      {result.overallBandScore >= 5.5 && result.overallBandScore < 6.5 && 'Modest User'}
                      {result.overallBandScore < 5.5 && 'Limited User'}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Right Column - Scores & Performance */}
          <div className="lg:col-span-2 space-y-6">
            {/* Performance Insights */}
            <PerformanceInsights
              scores={{
                listening: result.listeningBandScore,
                reading: result.readingBandScore,
                writing: result.writingBandScore,
                speaking: result.speakingBandScore,
                overall: result.overallBandScore,
              }}
            />

            {/* Enhanced Progress Chart with AI Predictions */}
            <EnhancedProgressChart
              scores={{
                listening: result.listeningBandScore,
                reading: result.readingBandScore,
                writing: result.writingBandScore,
                speaking: result.speakingBandScore,
                overall: result.overallBandScore,
              }}
              showPrediction={true}
            /></div>

          </div>
        </div>

        {/* Enhanced Footer */}
        <div className="mt-12 card-elevated text-center animate-slide-up" style={{ animationDelay: '750ms' }}>
          <div className="p-8">
            <div className="flex items-center justify-center mb-6">
              <div className="p-3 bg-primary-100 rounded-xl mr-3">
                <Award className="h-8 w-8 text-primary-600" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">Official IELTS Test Report</h3>
                <p className="text-sm text-muted-foreground">Internationally recognized certification</p>
              </div>
            </div>

            <div className="max-w-2xl mx-auto mb-6">
              <p className="text-gray-600 leading-relaxed mb-4">
                This is an official IELTS test result recognized by universities, employers, and immigration authorities worldwide.
                Your results are valid for 2 years from the test date.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="font-semibold text-gray-900 mb-1">Result ID</div>
                  <div className="font-mono text-gray-600">{result.id}</div>
                </div>
                {result.certificateSerial && (
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="font-semibold text-gray-900 mb-1">Certificate Serial</div>
                    <div className="font-mono text-gray-600">{result.certificateSerial}</div>
                  </div>
                )}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-6">
              <Link
                href="/search"
                className="btn-secondary inline-flex items-center"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Search Other Results
              </Link>
              {result.certificateSerial && (
                <Link
                  href={`/verify/${result.certificateSerial}`}
                  className="btn-primary inline-flex items-center"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Verify Certificate
                </Link>
              )}
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-xs text-muted-foreground">
                © 2024 IELTS Certification System. All rights reserved.
                Results are issued in accordance with IELTS standards and regulations.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
