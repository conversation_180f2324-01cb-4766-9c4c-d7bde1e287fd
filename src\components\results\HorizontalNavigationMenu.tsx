'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  TrendingUp,
  MessageSquare,
  Award,
  CheckCircle,
  Clock,
  ChevronRight
} from 'lucide-react';

interface HorizontalNavigationMenuProps {
  resultId: string;
  className?: string;
  completionStatus?: {
    overview: boolean;
    progress: boolean;
    feedback: boolean;
    certificate: boolean;
  };
}

export default function HorizontalNavigationMenu({ 
  resultId, 
  className = '',
  completionStatus = {
    overview: true,
    progress: true,
    feedback: true,
    certificate: false
  }
}: HorizontalNavigationMenuProps) {
  const pathname = usePathname();

  const menuItems = [
    {
      href: `/results/${resultId}`,
      label: 'Overview',
      icon: LayoutDashboard,
      description: 'Summary & Main Results',
      color: 'blue',
      completed: completionStatus.overview
    },
    {
      href: `/results/${resultId}/progress`,
      label: 'Progress',
      icon: TrendingUp,
      description: 'Historical Performance',
      color: 'emerald',
      completed: completionStatus.progress
    },
    {
      href: `/results/${resultId}/feedback`,
      label: 'Feedback',
      icon: MessageSquare,
      description: 'Detailed Analysis',
      color: 'amber',
      completed: completionStatus.feedback
    },
    {
      href: `/results/${resultId}/certificate`,
      label: 'Certificate',
      icon: Award,
      description: 'Official Document',
      color: 'purple',
      completed: completionStatus.certificate
    }
  ];

  const isActive = (href: string) => {
    if (href === `/results/${resultId}`) {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const getStatusIcon = (completed: boolean, isActive: boolean) => {
    if (completed) {
      return <CheckCircle className={`h-4 w-4 ${isActive ? 'text-primary-600' : 'text-success-500'}`} />;
    }
    return <Clock className={`h-4 w-4 ${isActive ? 'text-primary-600' : 'text-muted-foreground'}`} />;
  };

  const getColorClasses = (color: string, active: boolean) => {
    if (active) {
      return {
        container: 'bg-primary-50 border-primary-200 text-primary-900',
        icon: 'text-primary-600',
        title: 'text-primary-900',
        description: 'text-primary-700'
      };
    }

    const colorMap = {
      blue: {
        container: 'hover:bg-blue-50 hover:border-blue-200 border-transparent',
        icon: 'text-blue-500 group-hover:text-blue-600',
        title: 'text-gray-700 group-hover:text-blue-900',
        description: 'text-gray-500 group-hover:text-blue-700'
      },
      emerald: {
        container: 'hover:bg-emerald-50 hover:border-emerald-200 border-transparent',
        icon: 'text-emerald-500 group-hover:text-emerald-600',
        title: 'text-gray-700 group-hover:text-emerald-900',
        description: 'text-gray-500 group-hover:text-emerald-700'
      },
      amber: {
        container: 'hover:bg-amber-50 hover:border-amber-200 border-transparent',
        icon: 'text-amber-500 group-hover:text-amber-600',
        title: 'text-gray-700 group-hover:text-amber-900',
        description: 'text-gray-500 group-hover:text-amber-700'
      },
      purple: {
        container: 'hover:bg-purple-50 hover:border-purple-200 border-transparent',
        icon: 'text-purple-500 group-hover:text-purple-600',
        title: 'text-gray-700 group-hover:text-purple-900',
        description: 'text-gray-500 group-hover:text-purple-700'
      }
    };

    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className={`bg-white border-b border-gray-200 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav className="flex space-x-1 py-4 overflow-x-auto">
          {menuItems.map((item, index) => {
            const Icon = item.icon;
            const active = isActive(item.href);
            const colors = getColorClasses(item.color, active);

            return (
              <Link
                key={item.href}
                href={item.href}
                className={`group flex items-center px-4 py-3 rounded-xl border transition-all duration-200 focus-ring whitespace-nowrap min-w-fit ${colors.container}`}
              >
                <div className="flex items-center">
                  <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-white shadow-sm mr-3">
                    <Icon className={`h-4 w-4 transition-colors duration-200 ${colors.icon}`} />
                  </div>
                  <div className="flex-1">
                    <div className={`font-medium text-sm transition-colors duration-200 ${colors.title}`}>
                      {item.label}
                    </div>
                    <div className={`text-xs transition-colors duration-200 ${colors.description}`}>
                      {item.description}
                    </div>
                  </div>
                  <div className="ml-3 flex items-center space-x-2">
                    {getStatusIcon(item.completed, active)}
                    {!active && (
                      <ChevronRight className="h-3 w-3 text-gray-400 group-hover:text-gray-600" />
                    )}
                  </div>
                </div>
              </Link>
            );
          })}
        </nav>
      </div>
    </div>
  );
}
