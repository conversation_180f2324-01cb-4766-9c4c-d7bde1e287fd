'use client';

import React from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Award, 
  BarChart3, 
  Users,
  CheckCircle,
  AlertTriangle,
  Info,
  Star
} from 'lucide-react';

interface ScoreData {
  listening?: number | null;
  reading?: number | null;
  writing?: number | null;
  speaking?: number | null;
  overall?: number | null;
}

interface PerformanceInsightsProps {
  scores: ScoreData;
  className?: string;
}

export default function PerformanceInsights({ scores, className = '' }: PerformanceInsightsProps) {
  const globalAverages = {
    listening: 6.5,
    reading: 6.3,
    writing: 6.0,
    speaking: 6.2,
    overall: 6.5
  };

  const getBandLevel = (score: number) => {
    if (score >= 8.5) return { level: 'Expert User', color: 'emerald', description: 'Has fully operational command of the language' };
    if (score >= 7.5) return { level: 'Very Good User', color: 'blue', description: 'Has operational command with occasional inaccuracies' };
    if (score >= 6.5) return { level: 'Good User', color: 'indigo', description: 'Has generally effective command of the language' };
    if (score >= 5.5) return { level: 'Modest User', color: 'yellow', description: 'Has partial command of the language' };
    if (score >= 4.5) return { level: 'Limited User', color: 'orange', description: 'Basic competence is limited to familiar situations' };
    return { level: 'Extremely Limited User', color: 'red', description: 'No real communication is possible' };
  };

  const getModuleComparison = (score: number | null, globalAvg: number) => {
    if (!score) return { status: 'N/A', difference: 0, performance: 'neutral' };
    
    const difference = score - globalAvg;
    if (difference >= 1.5) return { status: 'Excellent', difference, performance: 'excellent' };
    if (difference >= 0.5) return { status: 'Above Average', difference, performance: 'good' };
    if (difference >= -0.5) return { status: 'Average', difference, performance: 'average' };
    if (difference >= -1.5) return { status: 'Below Average', difference, performance: 'below' };
    return { status: 'Needs Improvement', difference, performance: 'poor' };
  };

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'text-emerald-600 bg-emerald-50';
      case 'good': return 'text-blue-600 bg-blue-50';
      case 'average': return 'text-gray-600 bg-gray-50';
      case 'below': return 'text-yellow-600 bg-yellow-50';
      case 'poor': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const overallScore = scores.overall || 0;
  const overallLevel = getBandLevel(overallScore);
  const overallComparison = getModuleComparison(scores.overall, globalAverages.overall);

  // Calculate strengths and weaknesses
  const moduleScores = [
    { name: 'Listening', score: scores.listening, global: globalAverages.listening },
    { name: 'Reading', score: scores.reading, global: globalAverages.reading },
    { name: 'Writing', score: scores.writing, global: globalAverages.writing },
    { name: 'Speaking', score: scores.speaking, global: globalAverages.speaking }
  ].filter(module => module.score !== null);

  const strengths = moduleScores
    .filter(module => module.score! >= module.global + 0.5)
    .sort((a, b) => (b.score! - b.global) - (a.score! - a.global))
    .slice(0, 2);

  const weaknesses = moduleScores
    .filter(module => module.score! < module.global - 0.5)
    .sort((a, b) => (a.score! - a.global) - (b.score! - b.global))
    .slice(0, 2);

  // Calculate consistency
  const validScores = moduleScores.map(m => m.score!);
  const scoreRange = validScores.length > 0 ? Math.max(...validScores) - Math.min(...validScores) : 0;
  const consistency = scoreRange <= 1 ? 'Excellent' : scoreRange <= 2 ? 'Good' : 'Needs Improvement';

  return (
    <div className={`card-elevated animate-fade-in ${className}`}>
      <div className="p-6">
        <div className="flex items-center mb-6">
          <div className="p-2 bg-primary-100 rounded-lg mr-3">
            <BarChart3 className="h-5 w-5 text-primary-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Performance Insights</h3>
            <p className="text-sm text-muted-foreground">Comprehensive analysis against global benchmarks</p>
          </div>
        </div>

        {/* Overall Performance Summary */}
        <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <Award className="h-5 w-5 text-blue-600 mr-2" />
              <span className="font-semibold text-blue-900">Overall Performance</span>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-600">{overallScore}</div>
              <div className="text-xs text-blue-700">{overallLevel.level}</div>
            </div>
          </div>
          <p className="text-sm text-blue-800 mb-2">{overallLevel.description}</p>
          <div className="flex items-center text-sm">
            <span className="text-blue-700">Global Average: {globalAverages.overall}</span>
            <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getPerformanceColor(overallComparison.performance)}`}>
              {overallComparison.difference > 0 ? '+' : ''}{overallComparison.difference.toFixed(1)} vs global
            </span>
          </div>
        </div>

        {/* Module Performance Comparison */}
        <div className="mb-6">
          <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
            <Users className="h-4 w-4 mr-2 text-gray-600" />
            Module Performance vs Global Average
          </h4>
          <div className="grid grid-cols-2 gap-3">
            {moduleScores.map((module) => {
              const comparison = getModuleComparison(module.score, module.global);
              return (
                <div key={module.name} className="p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">{module.name}</span>
                    <span className="text-lg font-bold text-gray-900">{module.score}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">Global: {module.global}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPerformanceColor(comparison.performance)}`}>
                      {comparison.status}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Strengths and Weaknesses */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {/* Strengths */}
          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <h4 className="font-semibold text-green-800 mb-2 flex items-center">
              <CheckCircle className="h-4 w-4 mr-2" />
              Key Strengths
            </h4>
            {strengths.length > 0 ? (
              <div className="space-y-1">
                {strengths.map((strength) => (
                  <div key={strength.name} className="text-sm text-green-700">
                    • {strength.name}: {strength.score} (+{(strength.score! - strength.global).toFixed(1)} vs global)
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-green-700">All modules performing at or near global average</p>
            )}
          </div>

          {/* Areas for Improvement */}
          <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
            <h4 className="font-semibold text-orange-800 mb-2 flex items-center">
              <Target className="h-4 w-4 mr-2" />
              Focus Areas
            </h4>
            {weaknesses.length > 0 ? (
              <div className="space-y-1">
                {weaknesses.map((weakness) => (
                  <div key={weakness.name} className="text-sm text-orange-700">
                    • {weakness.name}: {weakness.score} ({(weakness.score! - weakness.global).toFixed(1)} vs global)
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-orange-700">Continue practicing to maintain current performance level</p>
            )}
          </div>
        </div>

        {/* Performance Insights */}
        <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
            <Info className="h-4 w-4 mr-2" />
            Performance Insights
          </h4>
          <div className="space-y-2 text-sm text-gray-700">
            <div className="flex items-center">
              <Star className="h-4 w-4 mr-2 text-yellow-500" />
              <span>Score Consistency: <strong>{consistency}</strong> (Range: {scoreRange.toFixed(1)} bands)</span>
            </div>
            {overallScore >= 7.5 && (
              <div className="flex items-center">
                <TrendingUp className="h-4 w-4 mr-2 text-green-500" />
                <span>Excellent performance! You're well above the global average with strong English proficiency.</span>
              </div>
            )}
            {overallScore >= 6.5 && overallScore < 7.5 && (
              <div className="flex items-center">
                <Target className="h-4 w-4 mr-2 text-blue-500" />
                <span>Good performance with solid foundation. Focus on weaker areas to reach the next level.</span>
              </div>
            )}
            {overallScore < 6.5 && (
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 mr-2 text-orange-500" />
                <span>Room for improvement. Consider focused practice in lower-scoring modules.</span>
              </div>
            )}
          </div>
        </div>

        {/* Band Score Interpretation */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <h5 className="font-medium text-blue-900 mb-2">What Your Band Score Means</h5>
          <p className="text-sm text-blue-800">{overallLevel.description}</p>
        </div>
      </div>
    </div>
  );
}
