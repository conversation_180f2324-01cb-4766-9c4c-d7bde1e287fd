'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  ArrowLeft,
  Award,
  Download,
  User,
  CheckCircle,
  AlertCircle,
  FileText,
  Shield,
  ExternalLink
} from 'lucide-react';
import NavigationMenu from '@/components/results/NavigationMenu';

interface TestResult {
  id: string;
  listeningBandScore: number | null;
  readingBandScore: number | null;
  writingBandScore: number | null;
  speakingBandScore: number | null;
  overallBandScore: number | null;
  certificateSerial: string | null;
  certificateGenerated: boolean;
  createdAt: string;
  candidate: {
    id: string;
    fullName: string;
    nationality: string;
    photoUrl: string | null;
  };
  testRegistration: {
    candidateNumber: string;
    testDate: string;
    testCenter: string;
  };
}

export default function CertificatePage() {
  const params = useParams();
  const resultId = params.id as string;

  const [result, setResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const fetchResult = useCallback(async () => {
    try {
      const response = await fetch(`/api/results/${resultId}`);
      if (response.ok) {
        const data = await response.json();
        setResult(data);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Result not found');
      }
    } catch (error) {
      console.error('Error fetching result:', error);
      setError('Failed to load result');
    } finally {
      setIsLoading(false);
    }
  }, [resultId]);

  useEffect(() => {
    fetchResult();
  }, [fetchResult]);

  const handleGenerateCertificate = async () => {
    setIsGenerating(true);
    try {
      const response = await fetch(`/api/results/${resultId}/generate-certificate`, {
        method: 'POST',
      });

      if (response.ok) {
        await response.json();
        // Refresh the result to get updated certificate info
        await fetchResult();
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to generate certificate');
      }
    } catch (error) {
      console.error('Error generating certificate:', error);
      setError('Failed to generate certificate');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownloadCertificate = async () => {
    setIsDownloading(true);
    try {
      const response = await fetch(`/api/certificate/${resultId}`);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `IELTS_Certificate_${result?.candidate.fullName.replace(/\s+/g, '_')}_${result?.certificateSerial}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        setError('Failed to download certificate');
      }
    } catch (error) {
      console.error('Error downloading certificate:', error);
      setError('Failed to download certificate');
    } finally {
      setIsDownloading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading certificate...</p>
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <AlertCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">Unable to Load Certificate</h3>
          <p className="text-gray-600 mb-6">{error || 'Result not found'}</p>
          <Link
            href="/search"
            className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Search
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/search" className="flex items-center text-blue-600 hover:text-blue-700 mr-4">
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back to Search
              </Link>
              <Award className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Official Certificate</h1>
                <p className="text-gray-600">IELTS Test Report Form</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Navigation Menu */}
          <div className="lg:col-span-1">
            <NavigationMenu resultId={resultId} />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Certificate Status */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Certificate Status</h2>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  {result.certificateGenerated ? (
                    <>
                      <CheckCircle className="h-8 w-8 text-green-500 mr-3" />
                      <div>
                        <h3 className="text-lg font-semibold text-green-800">Certificate Available</h3>
                        <p className="text-green-600">Your official IELTS certificate is ready for download</p>
                        {result.certificateSerial && (
                          <p className="text-sm text-gray-600 mt-1">
                            Serial Number: {result.certificateSerial}
                          </p>
                        )}
                      </div>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-8 w-8 text-yellow-500 mr-3" />
                      <div>
                        <h3 className="text-lg font-semibold text-yellow-800">Certificate Pending</h3>
                        <p className="text-yellow-600">Your certificate needs to be generated</p>
                      </div>
                    </>
                  )}
                </div>
                <div className="flex space-x-3">
                  {!result.certificateGenerated ? (
                    <button
                      onClick={handleGenerateCertificate}
                      disabled={isGenerating}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isGenerating ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Generating...
                        </>
                      ) : (
                        <>
                          <FileText className="h-4 w-4 mr-2" />
                          Generate Certificate
                        </>
                      )}
                    </button>
                  ) : (
                    <button
                      onClick={handleDownloadCertificate}
                      disabled={isDownloading}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isDownloading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Downloading...
                        </>
                      ) : (
                        <>
                          <Download className="h-4 w-4 mr-2" />
                          Download PDF
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Certificate Preview */}
            <div className="bg-white shadow rounded-lg p-8">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Certificate Preview</h2>

              {/* Certificate Design */}
              <div className="border-4 border-blue-600 rounded-lg p-8 bg-gradient-to-br from-blue-50 to-white">
                {/* Header */}
                <div className="text-center mb-8">
                  <div className="flex items-center justify-center mb-4">
                    <Award className="h-12 w-12 text-blue-600 mr-3" />
                    <div>
                      <h1 className="text-3xl font-bold text-blue-900">IELTS</h1>
                      <p className="text-sm text-blue-700">International English Language Testing System</p>
                    </div>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Test Report Form</h2>
                  <p className="text-gray-600">This is to certify that</p>
                </div>

                {/* Candidate Information */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                  <div className="md:col-span-2">
                    <div className="mb-6">
                      <h3 className="text-2xl font-bold text-center text-gray-900 mb-4 border-b-2 border-gray-300 pb-2">
                        {result.candidate.fullName}
                      </h3>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Candidate Number:</span>
                        <p className="text-gray-900">{result.testRegistration.candidateNumber}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Nationality:</span>
                        <p className="text-gray-900">{result.candidate.nationality}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Test Date:</span>
                        <p className="text-gray-900">{new Date(result.testRegistration.testDate).toLocaleDateString()}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Test Centre:</span>
                        <p className="text-gray-900">{result.testRegistration.testCenter}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-center">
                    {result.candidate.photoUrl ? (
                      <Image
                        src={result.candidate.photoUrl}
                        alt={result.candidate.fullName}
                        width={120}
                        height={150}
                        className="border-2 border-gray-300 rounded"
                      />
                    ) : (
                      <div className="w-30 h-36 bg-gray-200 border-2 border-gray-300 rounded flex items-center justify-center">
                        <User className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                  </div>
                </div>

                {/* Test Scores */}
                <div className="mb-8">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 text-center">Test Results</h3>
                  <div className="grid grid-cols-5 gap-4">
                    <div className="text-center">
                      <div className="font-medium text-gray-700 mb-2">Listening</div>
                      <div className="text-2xl font-bold text-blue-600">{result.listeningBandScore || 'N/A'}</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-gray-700 mb-2">Reading</div>
                      <div className="text-2xl font-bold text-green-600">{result.readingBandScore || 'N/A'}</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-gray-700 mb-2">Writing</div>
                      <div className="text-2xl font-bold text-yellow-600">{result.writingBandScore || 'N/A'}</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-gray-700 mb-2">Speaking</div>
                      <div className="text-2xl font-bold text-purple-600">{result.speakingBandScore || 'N/A'}</div>
                    </div>
                    <div className="text-center border-l-2 border-gray-300 pl-4">
                      <div className="font-medium text-gray-700 mb-2">Overall</div>
                      <div className="text-3xl font-bold text-indigo-600">{result.overallBandScore || 'N/A'}</div>
                    </div>
                  </div>
                </div>

                {/* Footer */}
                <div className="border-t-2 border-gray-300 pt-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-600">
                    <div>
                      <p className="font-medium">Certificate Details:</p>
                      <p>Result ID: {result.id}</p>
                      {result.certificateSerial && (
                        <p>Serial Number: {result.certificateSerial}</p>
                      )}
                      <p>Generated: {new Date(result.createdAt).toLocaleDateString()}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Verification:</p>
                      <p>This certificate can be verified online</p>
                      <p>Visit: ielts-verification.org</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Certificate Information */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6 flex items-center">
                <Shield className="h-6 w-6 text-blue-600 mr-2" />
                Certificate Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Validity & Usage</h3>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>• IELTS results are valid for 2 years from the test date</p>
                    <p>• This certificate is accepted by universities and immigration authorities worldwide</p>
                    <p>• The certificate shows your English proficiency level at the time of testing</p>
                    <p>• Original certificates are sent by post to your registered address</p>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Verification</h3>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>• Organizations can verify your results online using your details</p>
                    <p>• The verification service is available 24/7</p>
                    <p>• Additional Test Report Forms can be requested for a fee</p>
                    <p>• Contact your test center for any queries about your results</p>
                  </div>
                </div>
              </div>

              {result.certificateSerial && (
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center">
                    <ExternalLink className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <h4 className="font-medium text-blue-900">Verify This Certificate</h4>
                      <p className="text-sm text-blue-700">
                        Use serial number {result.certificateSerial} to verify this certificate online
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
